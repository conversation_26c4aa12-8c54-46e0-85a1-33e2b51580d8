# 操作日志显示逻辑优化

## 需求说明
根据操作结果的不同，显示不同的日志信息：
- **合格操作**：显示"完成一次操作"
- **不合格/不达标操作**：显示具体的错误信息

## 实现方案

### 1. 新增 getActionText 方法
在 `SimpleOperationLog.vue` 中添加了智能的文本显示逻辑：

```javascript
const getActionText = (item: any) => {
  // 如果是合格的操作，显示"完成一次操作"
  if (item.result === 1) {
    return '完成一次操作';
  }
  
  // 如果是不合格或不达标，显示具体的错误信息
  // 优先显示 actionName，如果没有则显示 actionLabels 中的描述
  if (item.actionName) {
    return item.actionName;
  }
  
  if (item.actionLabels && item.actionLabels.length > 0) {
    // 如果有多个标签，用逗号连接
    return item.actionLabels.map((label: any) => label.desc).join('、');
  }
  
  // 如果都没有，根据状态显示默认信息
  switch (item.result) {
    case 0:
      return '动作不达标：未双手操作';
    case 2:
      return '螺栓未达标';
    default:
      return '操作异常';
  }
};
```

### 2. 显示逻辑
- **result === 1 (合格)**：统一显示"完成一次操作"
- **result === 0 (不合格)** 或 **result === 2 (不达标)**：
  1. 优先显示 `item.actionName`（如果存在）
  2. 其次显示 `item.actionLabels` 中的描述信息（多个用"、"连接）
  3. 最后显示默认的错误信息

### 3. 模板更新
```html
<div class="log-action">{{ getActionText(item) }}</div>
```

## 显示效果示例

### 合格操作
```
✓ 完成一次操作                    合格
  2022-08-19 12:25:34
```

### 不合格操作（有具体信息）
```
⚠ 动作不达标：未双手操作            不合格
  2022-08-19 09:23:11
```

### 不达标操作（有标签信息）
```
⚠ 螺栓未达标                     不达标
  2022-08-17 12:25:34
```

## 优势

1. **智能显示**：根据操作结果自动选择合适的显示内容
2. **信息丰富**：不合格操作显示具体错误信息，便于用户了解问题
3. **统一体验**：合格操作统一显示，保持界面简洁
4. **容错处理**：即使数据不完整也能显示合理的默认信息
5. **多标签支持**：支持多个错误标签的组合显示

## 数据结构支持

组件支持以下数据结构：
```javascript
{
  result: 1,           // 操作结果：1-合格，0-不合格，2-不达标
  actionName: "...",   // 操作名称（可选）
  actionLabels: [      // 操作标签数组（可选）
    { desc: "错误描述1" },
    { desc: "错误描述2" }
  ],
  operationTime: "2022-08-19T12:25:34"
}
```

这样的设计既满足了合格操作的简洁显示需求，又能为不合格操作提供详细的错误信息，提升了用户体验。
