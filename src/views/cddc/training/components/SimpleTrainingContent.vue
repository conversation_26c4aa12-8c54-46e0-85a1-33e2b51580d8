<template>
  <div class="simple-training-content">
    <!-- 顶部进度区域 -->
    <div class="top-progress-area">
      <div class="progress-section">
        <SimpleProgressReport :data="trainProgress" />
      </div>
      <div class="stats-section">
        <SimpleTrainingStats :data="trainProgress" />
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content-area">
      <!-- 左侧摄像头画面 -->
      <div class="camera-section">
        <SimpleCameraView
          :show-status-tag="true"
          :status-result="realTimeData?.operationLog?.result"
          @connected="handleCameraConnected"
          @disconnected="handleCameraDisconnected"
          @error="handleCameraError"
        />
      </div>

      <!-- 右侧操作日志区域 -->
      <div class="operation-section">
        <EncouragementBar :data="trainProgress" />
        <SimpleOperationLog ref="operationLogRef" :detailId="trainId" :data="realTimeData" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SimpleCameraView from '/@/components/CameraView/SimpleCameraView.vue';
import SimpleOperationLog from './SimpleOperationLog.vue';
import SimpleProgressReport from './SimpleProgressReport.vue';
import SimpleTrainingStats from './SimpleTrainingStats.vue';
import EncouragementBar from './EncouragementBar.vue';
import type { RealTimeData, TrainProgress } from '../data';

// Props 定义
interface Props {
  trainId: string;
  realTimeData: RealTimeData;
  trainProgress: TrainProgress;
}

const props = defineProps<Props>();

// Emits 定义
interface Emits {
  (e: 'camera-connected'): void;
  (e: 'camera-disconnected'): void;
  (e: 'camera-error'): void;
}

const emit = defineEmits<Emits>();

// Refs
const operationLogRef = ref<any>(null);

// 摄像头事件处理
const handleCameraConnected = () => {
  emit('camera-connected');
};

const handleCameraDisconnected = () => {
  emit('camera-disconnected');
};

const handleCameraError = () => {
  emit('camera-error');
};

// 暴露方法给父组件
const clearOperationLog = () => {
  operationLogRef.value?.clearLocalLog();
};

defineExpose({
  clearOperationLog,
});
</script>

<style lang="less" scoped>
.simple-training-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  gap: 1.5vh;
  padding-bottom: 1.25vh;

  .top-progress-area {
    display: flex;
    gap: 1vw;
    flex-shrink: 0;

    .progress-section {
      flex: 1;
    }

    .stats-section {
      flex: 1;
    }
  }

  .main-content-area {
    display: flex;
    gap: 0.92vw;
    flex: 1;
    min-height: 0;

    .camera-section {
      min-height: 0;
      flex: 0 0 62.22vw; // 使用flex-basis替代固定width，保持原有比例
      max-width: 62.22vw; // 添加最大宽度限制
      background: url('@/assets/images/screen/train-simple-camera-bg.png') no-repeat center / 100%
        100%;
      padding: 1vh 1.5vw 3vh 1.4vw;
      position: relative;
    }

    .operation-section {
      min-height: 0;
      flex: 1;
      min-width: 0; // 防止内容溢出
      padding-top: 0.92vw;
      display: flex;
      flex-direction: column;
    }
  }
}

@media screen and (max-width: 1024px) {
  .simple-training-content {
    gap: 1.2vh;

    .top-progress-area {
      height: 120px;
      gap: 0.8vw;
    }

    .main-content-area {
      gap: 0.8vw;

      .camera-section {
        flex: 0 0 60vw; // 小屏幕下调整摄像头区域比例
        max-width: 60vw;
        padding: 0.8vh 1.2vw 2.5vh 1.2vw;
      }

      .operation-section {
        flex: 1;
        min-width: 0;
        max-width: calc(40vw - 0.8vw); // 确保右侧区域不会过宽
      }
    }
  }
}

// 超小屏幕适配 (MacBook Pro 13寸等)
@media screen and (max-width: 768px) {
  .simple-training-content {
    .main-content-area {
      flex-direction: column; // 小屏幕下改为垂直布局
      gap: 1vh;

      .camera-section {
        flex: none;
        width: 100%;
        max-width: none;
        aspect-ratio: 16/9; // 保持摄像头区域宽高比
      }

      .operation-section {
        flex: 1;
        max-width: none;
        padding-top: 0;
      }
    }
  }
}
</style>
