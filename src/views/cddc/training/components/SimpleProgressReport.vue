<script lang="ts" setup>
import { computed } from 'vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import type { V1LocationHomeProgressStatisticsRecordIDGetResponseResult } from '/@/api/cddc.model';
import ProgressBar from '/@/components/ClientPort/ProgressBar.vue';
const props = defineProps<{
  /**
   * 报表数据
   */
  data?: V1LocationHomeProgressStatisticsRecordIDGetResponseResult;
  loading?: boolean;
}>();

const renderData = computed(() => props.data || {});

// 秒转分钟
const secondsToMinutes = (seconds: number) => {
  return Math.floor(seconds / 60);
};

// 训练时长（转换为分钟）
const trainDurationInMinutes = computed(() => secondsToMinutes(renderData.value.trainDuration || 0));
const requestDurationInMinutes = computed(() => secondsToMinutes(renderData.value.requestDuration || 0));
</script>

<template>
  <div class="simple-progress-report">
    <ScreenTitle>训练进度</ScreenTitle>
    <a-spin wrapperClassName="spin-h-full" :spinning="props.loading">
      <ScreenBox class="progress-items h-full">
        <div class="progress-content">
          <!-- 左侧训练时长 -->
          <div class="progress-section">
            <div class="section-label">训练时长</div>
            <ProgressBar
              status="success"
              :current="trainDurationInMinutes"
              :total="requestDurationInMinutes"
              unit="min"
            />
          </div>

          <!-- 右侧训练次数 -->
          <div class="progress-section">
            <div class="section-label">训练次数</div>
            <ProgressBar
              status="error"
              :current="renderData.trainFrequency"
              :total="renderData.requestFrequency"
              unit="次"
            />
          </div>
        </div>
      </ScreenBox>
    </a-spin>
  </div>
</template>

<style lang="less" scoped>
.simple-progress-report {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
}

.progress-items {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 16px;
}

.progress-content {
  display: flex;
  align-items: center;
  height: 100%;
  gap: 0.93vw;
}

.progress-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  --ignore-color:rgba(62, 71, 81, 0.20);
  --ignore-color1:rgba(62, 71, 81, 0.00);
  background-image: linear-gradient(228deg, var(--ignore-color) 13.78%, var(--ignore-color1) 120.36%);
  padding: 0.74vw 0.93vw;
}

.section-label {
  font-size: 0.83vw;
  color: rgba(255, 255, 255, 0.8);
}

.progress-bar-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.progress-bar {
  flex: 1;
  height: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  border-radius: 6px;
  transition: width 0.3s ease;
}

.time-fill {
  background: linear-gradient(90deg, #00c9ff 0%, #92fe9d 100%);
}

.frequency-fill {
  background: linear-gradient(90deg, #ff6b6b 0%, #ffe66d 100%);
}

.progress-percent {
  font-size: 24px;
  font-weight: bold;
  min-width: 60px;
  text-align: right;
}

.time-percent {
  color: #00c9ff;
}

.frequency-percent {
  color: #ff6b6b;
}

.progress-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}

.divider {
  width: 1px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  align-self: center;
}

@media screen and (max-width: 1024px) {
  .progress-items {
    padding: 12px;
  }

  .progress-content {
    gap: 16px;
  }

  .progress-percent {
    font-size: 20px;
    min-width: 50px;
  }

  .divider {
    height: 60px;
  }
}
</style>
